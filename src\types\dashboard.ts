export interface Employee {
  id: string;
  name: string;
  email: string;
  initials: string;
  avatarColor: string;
  dcoins: number;
  achievements: number;
}

export interface ApiEmployee {
  name: string;
  email: string;
  dcoins: number;
  achievements: number;
}

export interface ProgressItem {
  label: string;
  percentage: number;
  color: string;
}

export interface BarChartData {
  label: string;
  value: number;
  color?: string;
}

export interface HorizontalBarData {
  label: string;
  value: number;
  percentage: number;
}

export interface InsightData {
  icon: string;
  value: string;
  label: string;
  color: string;
}

export interface DreamsData {
  percentage: number;
  registered: number;
  achieved: number;
}

export interface FinancialProfileEvolutionData {
  lessThan12: number;
  from12To17: number;
  from18To29: number;
  from30To59: number;
  greater60: number;
}

export interface FinancialSituationDataPoint {
  financialProfile: string;
  count: number;
  percentage: number;
}

export interface FinancialSituationMonthData {
  month: string;
  datapoints: FinancialSituationDataPoint[];
}

export interface FinancialEvolutionData {
  evolution: FinancialProfileEvolutionData;
  situations: FinancialSituationMonthData[];
}

export interface PersonalInterestsData {
  fashion: number;
  family: number;
  pets: number;
  travel: number;
  nature: number;
  music: number;
  selfCare: number;
  books: number;
  shopping: number;
  movies: number;
  games: number;
  fitness: number;
  technology: number;
  languages: number;
  astrology: number;
  food: number;
  work: number;
  culture: number;
  sports: number;
  spaceExploration: number;
  crypto: number;
  health: number;
}

export interface FinancialGoalsData {
  debtControl: number;
  moneySavings: number;
  strategicFund: number;
  futureInvestments: number;
  financialLiteracy: number;
}
