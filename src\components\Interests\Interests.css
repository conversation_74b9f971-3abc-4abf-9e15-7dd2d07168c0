/* Interests Component Styles */

.treemap {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr;
  grid-template-rows: 1.5fr 1fr 1.2fr;
  gap: 10px;
  height: 100%;
  min-height: 200px;
}

.treemap-item {
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  padding: 0.5rem;
  position: relative;
}

.interest-value {
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 0.25rem;
  opacity: 0.8;
}

.loading-message,
.error-message {
  grid-column: 1 / 4;
  grid-row: 1 / 4;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: var(--color-text-secondary);
  background-color: var(--color-border);
  border-radius: 8px;
}

.error-notice {
  font-size: 0.8rem;
  color: #ff9500;
  margin-bottom: 0.5rem;
  text-align: center;
}

/* Dynamic grid positioning for top interests */
.treemap-item-0 {
  grid-column: 1 / 2;
  grid-row: 1 / 3;
}

.treemap-item-1 {
  grid-column: 2 / 4;
  grid-row: 1 / 2;
}

.treemap-item-2 {
  grid-column: 2 / 3;
  grid-row: 2 / 3;
}

.treemap-item-3 {
  grid-column: 3 / 4;
  grid-row: 2 / 3;
}

.treemap-item-4 {
  grid-column: 1 / 2;
  grid-row: 3 / 4;
}

.treemap-item-5 {
  grid-column: 2 / 3;
  grid-row: 3 / 4;
}

.treemap-item-6 {
  grid-column: 3 / 4;
  grid-row: 3 / 4;
}
