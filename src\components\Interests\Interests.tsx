import React, { useState, useEffect } from 'react';
import { PersonalInterestsData } from '../../types/dashboard';
import { fetchPersonalInterests } from '../../services/api';
import './Interests.css';

interface InterestItem {
  key: keyof PersonalInterestsData;
  label: string;
  value: number;
  color: string;
}

const Interests: React.FC = () => {
  const [interests, setInterests] = useState<PersonalInterestsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchPersonalInterests();
        setInterests(data);
      } catch (error) {
        console.error('Error fetching interests:', error);
        setError('Erro ao carregar interesses. Usando dados estáticos.');
        // Fallback to static data
        setInterests({
          fashion: 25,
          family: 48,
          pets: 22,
          travel: 45,
          nature: 19,
          music: 31,
          selfCare: 4,
          books: 1,
          shopping: 4,
          movies: 2,
          games: 2,
          fitness: 3,
          technology: 3,
          languages: 2,
          astrology: 1,
          food: 0,
          work: 1,
          culture: 3,
          sports: 0,
          spaceExploration: 4,
          crypto: 0,
          health: 0
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Map API keys to Portuguese labels
  const labelMap: Record<keyof PersonalInterestsData, string> = {
    fashion: 'Moda',
    family: 'Família',
    pets: 'Pets',
    travel: 'Viagens',
    nature: 'Natureza',
    music: 'Música',
    selfCare: 'Autocuidado',
    books: 'Livros',
    shopping: 'Compras',
    movies: 'Filmes',
    games: 'Jogos',
    fitness: 'Fitness',
    technology: 'Tecnologia',
    languages: 'Idiomas',
    astrology: 'Astrologia',
    food: 'Culinária',
    work: 'Trabalho',
    culture: 'Cultura',
    sports: 'Esportes',
    spaceExploration: 'Exploração Espacial',
    crypto: 'Criptomoedas',
    health: 'Saúde'
  };

  const getTopInterests = (data: PersonalInterestsData): InterestItem[] => {
    const colors = [
      'rgba(90, 200, 250, 0.4)',   // Blue
      'rgba(255, 59, 48, 0.3)',    // Red
      'rgba(175, 82, 222, 0.4)',   // Purple
      'rgba(52, 199, 89, 0.4)',    // Green
      'rgba(255, 149, 0, 0.3)',    // Orange
      'rgba(175, 82, 222, 0.3)',   // Purple variant
      'rgba(52, 199, 89, 0.3)'     // Green variant
    ];

    return Object.entries(data)
      .map(([key, value], index) => ({
        key: key as keyof PersonalInterestsData,
        label: labelMap[key as keyof PersonalInterestsData],
        value: Number(value),
        color: colors[index % colors.length] // Use consistent color assignment
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 7); // Get top 7 interests
  };

  if (loading) {
    return (
      <article className="card card-interests">
        <h2 className="card-title">Interesses dos Colaboradores</h2>
        <div className="treemap">
          <div className="loading-message">Carregando interesses...</div>
        </div>
      </article>
    );
  }

  if (!interests) {
    return (
      <article className="card card-interests">
        <h2 className="card-title">Interesses dos Colaboradores</h2>
        <div className="treemap">
          <div className="error-message">Erro ao carregar dados</div>
        </div>
      </article>
    );
  }

  const topInterests = getTopInterests(interests);

  return (
    <article className="card card-interests">
      <h2 className="card-title">Interesses dos Colaboradores</h2>
      {error && <div className="error-notice">{error}</div>}
      <div className="treemap">
        {topInterests.map((interest, index) => (
          <div
            key={interest.key}
            className={`treemap-item treemap-item-${index}`}
            style={{ backgroundColor: interest.color }}
          >
            {interest.label}
            <span className="interest-value">{interest.value}</span>
          </div>
        ))}
      </div>
    </article>
  );
};

export default Interests;