import React, { useState, useEffect } from 'react';
import { FinancialEvolutionData, FinancialSituationMonthData, FinancialSituationDataPoint } from '../../types/dashboard';
import { fetchFinancialEvolutionData } from '../../services/api';
import './FinancialEvolution.css';

interface TooltipData {
  x: number;
  y: number;
  month: string;
  profile: string;
  count: number;
  percentage: number;
  visible: boolean;
}

const FinancialEvolution: React.FC = () => {
  const [data, setData] = useState<FinancialEvolutionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tooltip, setTooltip] = useState<TooltipData>({
    x: 0,
    y: 0,
    month: '',
    profile: '',
    count: 0,
    percentage: 0,
    visible: false
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const evolutionData = await fetchFinancialEvolutionData();
        console.log('Financial evolution data received:', evolutionData);

        // Validate the data structure
        if (!evolutionData) {
          throw new Error('No data received from API');
        }

        if (!evolutionData.situations || !Array.isArray(evolutionData.situations)) {
          console.warn('Invalid situations data structure:', evolutionData.situations);
          throw new Error('Invalid data structure received from API');
        }

        console.log('Number of months in data:', evolutionData.situations.length);
        console.log('Months:', evolutionData.situations.map(s => s.month));
        setData(evolutionData);
      } catch (err) {
        setError('Erro ao carregar dados de evolução financeira');
        console.error('Error loading financial evolution data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Color mapping for financial profiles
  const profileColors: Record<string, string> = {
    'Undefined': '#8e8e93',
    'Overindebted': '#ff3b30',
    'Indebted': '#af52de',
    'Balanced': '#007aff',
    'Investor': '#34c759'
  };

  // Portuguese labels for financial profiles
  const profileLabels: Record<string, string> = {
    'Undefined': 'Não definido',
    'Overindebted': 'Superendividados',
    'Indebted': 'Endividados',
    'Balanced': 'Equilibrados',
    'Investor': 'Investidores'
  };

  const formatMonth = (monthStr: string): string => {
    const [year, month] = monthStr.split('-');
    const monthNames = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];
    return `${monthNames[parseInt(month) - 1]}/${year}`;
  };

  const renderChart = () => {
    if (!data || !data.situations || !Array.isArray(data.situations) || data.situations.length === 0) {
      return <div className="no-data">Nenhum dado disponível</div>;
    }

    // --- IMPROVED RESPONSIVE CHART DIMENSIONS ---

    // Dynamic chart dimensions that scale with container
    const chartWidth = 1000; // Increased width for better utilization
    const chartHeight = 300; // Increased height for better visibility

    // Reduced margins for better space utilization
    const margin = { top: 10, right: 40, bottom: 20, left: 60 }; // Reduced top from 30 to 10, bottom from 60 to 20

    // Calculate the dimensions of the actual plot area (inside the margins)
    const plotWidth = chartWidth - margin.left - margin.right;
    const plotHeight = chartHeight - margin.top - margin.bottom;

    // --- END OF IMPROVED DIMENSIONS ---


    // Get all unique profiles with proper validation
    const allProfiles = Array.from(new Set(
      data.situations
        .filter(month => month && Array.isArray(month.datapoints))
        .flatMap(month =>
          month.datapoints
            .filter(dp => dp && dp.financialProfile)
            .map(dp => dp.financialProfile)
        )
    ));

    // 3. Update month positions to be within the new plotWidth and respect the left margin
    const monthPositions = data.situations.map((_, index) =>
      margin.left + (index * plotWidth) / Math.max(data.situations.length - 1, 1)
    );

    // Find max percentage for scaling with proper validation
    const maxPercentage = 100; // Fixed to 100% for consistency

    // 4. Update the Y-scale function to map to the new plotHeight and respect margins
    const scaleY = (percentage: number) =>
      chartHeight - margin.bottom - (percentage / maxPercentage) * plotHeight;

    return (
      <div className="line-chart-container">
        <svg
          width="100%"
          height="100%"
          viewBox={`0 0 ${chartWidth} ${chartHeight}`}
          preserveAspectRatio="xMidYMid meet"
        >
          {/* Grid lines and Y-axis labels */}
          {[0, 25, 50, 75, 100].map(percentage => (
            <g key={percentage}>
              <line
                // 5. Adjust grid lines to fit the new plot area
                x1={margin.left}
                y1={scaleY(percentage)}
                x2={chartWidth - margin.right}
                y2={scaleY(percentage)}
                stroke="#333344"
                strokeWidth="1"
                opacity="0.2"
              />
              <text
                // 6. Position Y-axis labels to the left of the plot area
                x={margin.left - 15}
                y={scaleY(percentage)}
                textAnchor="end"
                dominantBaseline="middle"
                fontSize="14" // Larger font size for better readability
                fill="#a0a0b0" // Better contrast color
                fontWeight="500"
              >
                {percentage}%
              </text>
            </g>
          ))}

          {/* Render lines for each profile */}
          {allProfiles.map(profile => {
            const profileData = data.situations.map(month => {
              const datapoint = month?.datapoints.find(dp => dp.financialProfile === profile);
              return datapoint ? datapoint.percentage : 0;
            });

            const points = profileData.map((percentage, index) =>
              `${monthPositions[index]},${scaleY(percentage)}`
            ).join(' ');

            return (
              <g key={profile}>
                <polyline
                  fill="none"
                  stroke={profileColors[profile] || '#8e8e93'}
                  strokeWidth="4" // Thicker lines for better visibility
                  points={points}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                {/* Data points */}
                {profileData.map((percentage, index) => {
                  const monthData = data.situations[index];
                  const datapoint = monthData?.datapoints.find(dp => dp.financialProfile === profile);

                  // Only render circles for data points that actually exist and have data
                  if (!monthData || !datapoint) {
                    return null;
                  }

                  return (
                    <circle
                      key={`${profile}-${index}`}
                      cx={monthPositions[index]}
                      cy={scaleY(percentage)}
                      r="6" // Larger data points for better visibility
                      fill={profileColors[profile] || '#8e8e93'}
                      stroke="none" // Remove all borders to match the gray dot style
                      style={{ cursor: 'pointer' }}
                      onMouseEnter={(e) => {
                        // Use mouse position directly for better alignment
                        const rect = e.currentTarget.getBoundingClientRect();
                        const containerElement = e.currentTarget.closest('.line-chart-container');

                        if (containerElement) {
                          const containerRect = containerElement.getBoundingClientRect();

                          // Calculate position relative to the container
                          const x = rect.left + rect.width / 2 - containerRect.left;
                          const y = rect.top - containerRect.top - 10; // 10px above the dot

                          setTooltip({
                            x,
                            y,
                            month: formatMonth(monthData.month),
                            profile: profileLabels[profile] || profile,
                            count: datapoint.count,
                            percentage: datapoint.percentage,
                            visible: true
                          });
                        }
                      }}
                      onMouseLeave={() => {
                        setTooltip(prev => ({ ...prev, visible: false }));
                      }}
                    />
                  );
                })}
              </g>
            );
          })}

          {/* Month labels */}
          {data.situations.map((month, index) => (
            <text
              key={month.month}
              x={monthPositions[index]}
              // Position the labels within the bottom margin area with more spacing
              y={chartHeight - margin.bottom + 35}
              textAnchor="middle"
              fontSize="15" // Larger font size for better readability
              fill="#a0a0b0" // Better contrast color
              fontWeight="500"
            >
              {formatMonth(month.month)}
            </text>
          ))}
        </svg>

        {/* Tooltip */}
        {tooltip.visible && (
          <div
            className="chart-tooltip"
            style={{
              position: 'absolute',
              left: tooltip.x,
              top: tooltip.y,
              transform: 'translateX(-50%) translateY(-100%)',
              zIndex: 9999,
              pointerEvents: 'none'
            }}
          >
            <div className="tooltip-content">
              <div className="tooltip-title">{tooltip.month}</div>
              <div className="tooltip-profile">{tooltip.profile}</div>
              <div className="tooltip-stats">
                <div>Quantidade: {tooltip.count}</div>
                <div>Percentual: {tooltip.percentage.toFixed(1)}%</div>
              </div>
            </div>
          </div>
        )}

      </div>
    );
  };

  if (loading) {
    return (
      <article className="card card-financial-evolution">
        <h2 className="card-title">Evolução do Perfil Financeiro</h2>
        <div className="loading-state">Carregando...</div>
      </article>
    );
  }

  if (error) {
    return (
      <article className="card card-financial-evolution">
        <h2 className="card-title">Evolução do Perfil Financeiro</h2>
        <div className="error-state">{error}</div>
      </article>
    );
  }

  // Get the latest month's data for the legend
  const getLatestMonthData = () => {
    if (!data || !data.situations || data.situations.length === 0) {
      return {};
    }

    const latestMonth = data.situations[data.situations.length - 1];
    const latestData: Record<string, number> = {};

    if (latestMonth && latestMonth.datapoints) {
      latestMonth.datapoints.forEach(dp => {
        if (dp && dp.financialProfile && typeof dp.percentage === 'number') {
          latestData[dp.financialProfile] = dp.percentage;
        }
      });
    }

    return latestData;
  };

  const latestMonthData = getLatestMonthData();

  return (
    <article className="card card-financial-evolution">
      <h2 className="card-title">Evolução do Perfil Financeiro</h2>
      {renderChart()}
      <div className="line-chart-legend">
        {Object.entries(profileLabels).map(([key, label]) => {
          const percentage = latestMonthData[key] || 0;
          const displayText = percentage > 0
            ? `${label} (${percentage.toFixed(1)}%)`
            : label;

          return (
            <div key={key} className="legend-item">
              <div
                className="legend-color-box"
                style={{ backgroundColor: profileColors[key] }}
              />
              {displayText}
            </div>
          );
        })}
      </div>
    </article>
  );
};

export default FinancialEvolution;